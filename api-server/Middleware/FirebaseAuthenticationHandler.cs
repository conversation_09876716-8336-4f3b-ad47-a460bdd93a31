using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text.Encodings.Web;
using VidCompressor.Services;

namespace VidCompressor.ApiServer.Middleware;

/// <summary>
/// Custom authentication handler for Firebase ID tokens
/// </summary>
public class FirebaseAuthenticationHandler : AuthenticationHandler<AuthenticationSchemeOptions>
{
    private readonly IFirebaseAuthService _firebaseAuthService;

    public FirebaseAuthenticationHandler(
        IOptionsMonitor<AuthenticationSchemeOptions> options,
        ILoggerFactory logger,
        UrlEncoder encoder,
        IFirebaseAuthService firebaseAuthService)
        : base(options, logger, encoder)
    {
        _firebaseAuthService = firebaseAuthService;
    }

    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        // Check if Authorization header exists
        if (!Request.Headers.ContainsKey("Authorization"))
        {
            return AuthenticateResult.NoResult();
        }

        var authHeader = Request.Headers["Authorization"].ToString();
        if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
        {
            return AuthenticateResult.NoResult();
        }

        var token = authHeader.Substring("Bearer ".Length).Trim();
        if (string.IsNullOrEmpty(token))
        {
            return AuthenticateResult.NoResult();
        }

        try
        {
            // Verify the Firebase ID token
            var decodedToken = await _firebaseAuthService.VerifyIdTokenAsync(token);
            
            // Create claims from the Firebase token
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, decodedToken.Uid),
                new Claim(ClaimTypes.Email, decodedToken.Claims.GetValueOrDefault("email")?.ToString() ?? ""),
                new Claim("firebase_uid", decodedToken.Uid)
            };

            // Add custom claims if they exist
            if (decodedToken.Claims.ContainsKey("firstName"))
            {
                claims.Add(new Claim("firstName", decodedToken.Claims["firstName"].ToString() ?? ""));
            }

            if (decodedToken.Claims.ContainsKey("profilePictureUrl"))
            {
                claims.Add(new Claim("profilePictureUrl", decodedToken.Claims["profilePictureUrl"].ToString() ?? ""));
            }

            var identity = new ClaimsIdentity(claims, Scheme.Name);
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, Scheme.Name);

            return AuthenticateResult.Success(ticket);
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "Failed to validate Firebase ID token");
            return AuthenticateResult.Fail("Invalid Firebase ID token");
        }
    }
}
