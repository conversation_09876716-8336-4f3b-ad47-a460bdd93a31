using Microsoft.AspNetCore.Authentication;
using VidCompressor.Data;
using VidCompressor.Services;
using VidCompressor.ApiServer.Middleware;
using VidCompressor.Extensions;
using VidCompressor.Configuration;
using Stripe;

var builder = WebApplication.CreateBuilder(args);

// Add Google Secret Manager as a configuration source
// This loads secrets directly at startup - no local files needed!
builder.Configuration.AddSecretManager(builder.Configuration);

// Set Firestore emulator environment variable early
var firestoreConfig = builder.Configuration.GetSection("Firestore");
if (firestoreConfig.GetValue<bool>("UseEmulator"))
{
    var emulatorHost = firestoreConfig.GetValue<string>("EmulatorHost");
    if (!string.IsNullOrEmpty(emulatorHost))
    {
        Environment.SetEnvironmentVariable("FIRESTORE_EMULATOR_HOST", emulatorHost);
        Console.WriteLine($"[API-SERVER] Set FIRESTORE_EMULATOR_HOST to: {emulatorHost}");
    }
}

// Initialize Stripe API key
var stripeSecret = builder.Configuration["Stripe:SecretKey"];
if (!string.IsNullOrWhiteSpace(stripeSecret))
{
    StripeConfiguration.ApiKey = stripeSecret;
}
else
{
    Console.WriteLine("[API-SERVER] Warning: Stripe:SecretKey is not configured. Stripe features will not work.");
}

// Add services to the container.
builder.Services.AddFirestore(builder.Configuration);
builder.Services.AddFirebase(builder.Configuration);

builder.Services.AddAuthentication("Firebase")
    .AddScheme<AuthenticationSchemeOptions, VidCompressor.ApiServer.Middleware.FirebaseAuthenticationHandler>("Firebase", options => { });

// Configure Google Cloud settings
builder.Services.Configure<GoogleCloudConfig>(builder.Configuration.GetSection("GoogleCloud"));

builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend",
        builder =>
        {
            var allowedOrigins = new List<string> { "http://localhost:3000","http://localhost:5000","http://127.0.0.1:5000" }; // Local development

            // Add Firebase Hosting URLs and custom domain for production
            if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Production")
            {
                allowedOrigins.Add("https://tranquil-bison-465923-v9.web.app");
                allowedOrigins.Add("https://tranquil-bison-465923-v9.firebaseapp.com");
                allowedOrigins.Add("https://gallerytuner.com");
                allowedOrigins.Add("https://www.gallerytuner.com");
            }

            builder.WithOrigins(allowedOrigins.ToArray())
                   .AllowAnyHeader()
                   .AllowAnyMethod();
        });
});

builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.Converters.Add(new System.Text.Json.Serialization.JsonStringEnumConverter());
    });
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

builder.Services.AddHttpClient();

// Add shared services
builder.Services.AddScoped<GooglePhotosService>();
builder.Services.AddScoped<GoogleCloudStorageService>();
builder.Services.AddScoped<GoogleTranscoderService>();
builder.Services.AddScoped<CloudTasksService>();
builder.Services.AddScoped<CreditsService>();
builder.Services.AddScoped<PreviewCacheService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Add global exception handling
app.UseMiddleware<GlobalExceptionMiddleware>();

if (!app.Environment.IsDevelopment())
{
    app.UseHttpsRedirection();
}

app.UseCors("AllowFrontend");

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Add a simple health check endpoint for Cloud Run startup probe
app.MapGet("/health", () => Results.Ok(new { status = "healthy", timestamp = DateTime.UtcNow }));

app.Run();
