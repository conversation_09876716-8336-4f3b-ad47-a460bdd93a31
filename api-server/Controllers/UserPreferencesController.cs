using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using VidCompressor.Repositories;

namespace VidCompressor.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class UserPreferencesController : ControllerBase
{
    private readonly IUserRepository _userRepository;
    private readonly ILogger<UserPreferencesController> _logger;

    public UserPreferencesController(IUserRepository userRepository, ILogger<UserPreferencesController> logger)
    {
        _userRepository = userRepository;
        _logger = logger;
    }

    /// <summary>
    /// Get current user's preferences
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetPreferences()
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new { message = "User not authenticated" });
            }

            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return NotFound(new { message = "User not found" });
            }

            return Ok(new
            {
                defaultUploadToGooglePhotos = user.DefaultUploadToGooglePhotos,
                notificationsEnabled = user.NotificationsEnabled
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user preferences for user {UserId}", User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
            return StatusCode(500, new { message = "Error retrieving user preferences" });
        }
    }

    /// <summary>
    /// Update current user's preferences
    /// </summary>
    [HttpPut]
    public async Task<IActionResult> UpdatePreferences([FromBody] UpdatePreferencesRequest request)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new { message = "User not authenticated" });
            }

            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return NotFound(new { message = "User not found" });
            }

            // Update preferences
            if (request.DefaultUploadToGooglePhotos.HasValue)
            {
                user.DefaultUploadToGooglePhotos = request.DefaultUploadToGooglePhotos.Value;
            }

            if (request.NotificationsEnabled.HasValue)
            {
                user.NotificationsEnabled = request.NotificationsEnabled.Value;
            }

            await _userRepository.UpdateAsync(user);

            _logger.LogInformation("Updated preferences for user {UserId}", userId);

            return Ok(new
            {
                defaultUploadToGooglePhotos = user.DefaultUploadToGooglePhotos,
                notificationsEnabled = user.NotificationsEnabled
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user preferences for user {UserId}", User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
            return StatusCode(500, new { message = "Error updating user preferences" });
        }
    }
}

/// <summary>
/// Request model for updating user preferences
/// </summary>
public class UpdatePreferencesRequest
{
    public bool? DefaultUploadToGooglePhotos { get; set; }
    public bool? NotificationsEnabled { get; set; }
}
