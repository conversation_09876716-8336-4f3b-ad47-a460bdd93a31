using FirebaseAdmin;
using FirebaseAdmin.Auth;
using Google.Apis.Auth.OAuth2;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace VidCompressor.Services;

/// <summary>
/// Firebase authentication service implementation
/// </summary>
public class FirebaseAuthService : IFirebaseAuthService
{
    private readonly ILogger<FirebaseAuthService> _logger;
    private readonly FirebaseAuth _firebaseAuth;

    public FirebaseAuthService(ILogger<FirebaseAuthService> logger, IConfiguration configuration)
    {
        _logger = logger;

        // Initialize Firebase Admin SDK if not already initialized
        if (FirebaseApp.DefaultInstance == null)
        {
            var projectId = configuration["GoogleCloud:ProjectId"] ??
                           configuration["Firestore:ProjectId"] ??
                           throw new InvalidOperationException("Project ID not configured");

            try
            {
                var credential = GoogleCredential.GetApplicationDefault();
                _logger.LogInformation("Successfully obtained Application Default Credentials");

                // Use the default Firebase Admin SDK service account
                var serviceAccountId = $"firebase-adminsdk-fbsvc@{projectId}.iam.gserviceaccount.com";

                FirebaseApp.Create(new AppOptions()
                {
                    Credential = credential,
                    ProjectId = projectId,
                    ServiceAccountId = serviceAccountId
                });

                _logger.LogInformation("Firebase Admin SDK initialized successfully for project: {ProjectId} with service account: {ServiceAccountId}",
                    projectId, serviceAccountId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize Firebase Admin SDK for project: {ProjectId}", projectId);
                throw new InvalidOperationException($"Failed to initialize Firebase Admin SDK: {ex.Message}", ex);
            }
        }

        _firebaseAuth = FirebaseAuth.DefaultInstance;
    }

    public async Task<string> CreateCustomTokenAsync(string userId, Dictionary<string, object>? additionalClaims = null)
    {
        try
        {
            _logger.LogInformation("Creating custom Firebase token for user: {UserId}", userId);

            if (string.IsNullOrEmpty(userId))
            {
                throw new ArgumentException("User ID cannot be null or empty", nameof(userId));
            }

            var customToken = await _firebaseAuth.CreateCustomTokenAsync(userId, additionalClaims);

            _logger.LogInformation("Successfully created custom Firebase token for user: {UserId}", userId);
            return customToken;
        }
        catch (FirebaseAuthException ex)
        {
            _logger.LogError(ex, "Firebase Auth error creating custom token for user: {UserId}. Error code: {ErrorCode}, Message: {Message}",
                userId, ex.AuthErrorCode, ex.Message);
            throw new InvalidOperationException($"Firebase Auth error: {ex.Message} (Code: {ex.AuthErrorCode})", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error creating custom Firebase token for user: {UserId}. Type: {ExceptionType}, Message: {Message}",
                userId, ex.GetType().Name, ex.Message);
            throw;
        }
    }

    public async Task<FirebaseToken> VerifyIdTokenAsync(string idToken)
    {
        try
        {
            _logger.LogDebug("Verifying Firebase ID token");
            
            var decodedToken = await _firebaseAuth.VerifyIdTokenAsync(idToken);
            
            _logger.LogDebug("Successfully verified Firebase ID token for user: {UserId}", decodedToken.Uid);
            return decodedToken;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to verify Firebase ID token");
            throw;
        }
    }

    public async Task<UserRecord?> GetUserAsync(string userId)
    {
        try
        {
            _logger.LogDebug("Getting Firebase user: {UserId}", userId);
            
            var userRecord = await _firebaseAuth.GetUserAsync(userId);
            
            _logger.LogDebug("Successfully retrieved Firebase user: {UserId}", userId);
            return userRecord;
        }
        catch (FirebaseAuthException ex) when (ex.AuthErrorCode == AuthErrorCode.UserNotFound)
        {
            _logger.LogDebug("Firebase user not found: {UserId}", userId);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get Firebase user: {UserId}", userId);
            throw;
        }
    }
}
