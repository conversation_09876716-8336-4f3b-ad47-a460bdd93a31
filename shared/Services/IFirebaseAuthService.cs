using FirebaseAdmin.Auth;

namespace VidCompressor.Services;

/// <summary>
/// Service for Firebase authentication operations
/// </summary>
public interface IFirebaseAuthService
{
    /// <summary>
    /// Create a custom Firebase token for the given user
    /// </summary>
    /// <param name="userId">User ID to create token for</param>
    /// <param name="additionalClaims">Additional claims to include in the token</param>
    /// <returns>Firebase custom token</returns>
    Task<string> CreateCustomTokenAsync(string userId, Dictionary<string, object>? additionalClaims = null);

    /// <summary>
    /// Verify a Firebase ID token
    /// </summary>
    /// <param name="idToken">Firebase ID token to verify</param>
    /// <returns>Decoded token with user information</returns>
    Task<FirebaseToken> VerifyIdTokenAsync(string idToken);

    /// <summary>
    /// Get user information from Firebase Auth
    /// </summary>
    /// <param name="userId">User ID to get information for</param>
    /// <returns>User record from Firebase Auth</returns>
    Task<UserRecord?> GetUserAsync(string userId);
}
