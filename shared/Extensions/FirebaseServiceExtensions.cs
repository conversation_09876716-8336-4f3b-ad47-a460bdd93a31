using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using VidCompressor.Services;

namespace VidCompressor.Extensions;

/// <summary>
/// Extension methods for registering Firebase services
/// </summary>
public static class FirebaseServiceExtensions
{
    /// <summary>
    /// Add Firebase services to the service collection
    /// </summary>
    public static IServiceCollection AddFirebase(this IServiceCollection services, IConfiguration configuration)
    {
        // Register Firebase Auth service
        services.AddSingleton<IFirebaseAuthService, FirebaseAuthService>();
        
        return services;
    }
}
