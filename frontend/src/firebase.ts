import { initializeApp, getApp, getApps } from 'firebase/app';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

// Firebase configuration - ensure these are correct
const firebaseConfig = {
  apiKey: "AIzaSyDtku5vn2-ZZd7Ku2BH_hY3-fSlJ_yBmlM",
  authDomain: "tranquil-bison-465923-v9.firebaseapp.com",
  projectId: "tranquil-bison-465923-v9",
  storageBucket: "tranquil-bison-465923-v9.firebasestorage.app",
  messagingSenderId: "546390650743",
  appId: "1:546390650743:web:6732df22854dec04397b72"
};

// Initialize Firebase App safely for environments with hot-reloading
const app = !getApps().length ? initializeApp(firebaseConfig) : getApp();

// Get the Firestore instance for the SPECIFIC database ID
const db = getFirestore(app, 'gallerytuner');

// Initialize Firebase Auth
const auth = getAuth(app);

// Connect to Firestore emulator in development
if (process.env.NODE_ENV === 'development' && process.env.REACT_APP_USE_FIRESTORE_EMULATOR === 'true') {
  console.log('Connecting to Firestore emulator at localhost:8080...');
  connectFirestoreEmulator(db, 'localhost', 8080);
} else {
  console.log('Using production Firestore database: gallerytuner');
}

export { db, auth };