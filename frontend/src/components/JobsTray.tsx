import React from 'react';
import {
  <PERSON>,
  Paper,
  Typo<PERSON>,
  IconButton,
  <PERSON>,
  But<PERSON>,
  List,
  Divider,
  Backdrop,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  PlaylistPlay,
  KeyboardArrowUp,
  KeyboardArrowDown,
  Notifications
} from '@mui/icons-material';
import JobItem from './JobItem';
import { JobData } from './JobsPanel';
import {
  isNotificationSupported,
  getNotificationPermission,
  requestNotificationPermission,
  hasBeenPromptedForNotifications,
  markAsPromptedForNotifications
} from '../utils/notifications';

interface JobsTrayProps {
  // Panel state
  isCollapsed: boolean;
  onToggleCollapse: () => void;

  // Jobs data
  jobs: JobData[];
  onClearJob: (jobId: string) => void;
  onClearAllJobs: () => void;

  // Media items for preview images
  mediaItems: Array<{
    id: string;
    filename: string;
    mimeType: string;
    baseUrl: string;
  }>;

  // Token for API calls
  token: string | null;

  // Notification settings
  notificationsEnabled?: boolean;
  onNotificationsChange?: (enabled: boolean) => void;
}

const JobsTray: React.FC<JobsTrayProps> = ({
  isCollapsed,
  onToggleCollapse,
  jobs,
  onClearJob,
  onClearAllJobs,
  mediaItems,
  token,
  notificationsEnabled = true,
  onNotificationsChange
}) => {
  // Count active jobs for status display
  const activeJobs = jobs.filter(job => !['completed', 'failed', 'cancelled'].includes(job.status.toLowerCase()));
  const hasActiveJobs = activeJobs.length > 0;
  const [showNotificationPrompt, setShowNotificationPrompt] = React.useState(false);

  // Show notification prompt for first-time users when they start their first job
  React.useEffect(() => {
    if (activeJobs.length > 0 &&
        !hasBeenPromptedForNotifications() &&
        isNotificationSupported() &&
        getNotificationPermission() === 'default') {
      setShowNotificationPrompt(true);
    }
  }, [activeJobs.length]);



  // Sort jobs by creation date (newest first)
  const sortedJobs = [...jobs].sort((a, b) => {
    const dateA = new Date(a.createdAt || 0).getTime();
    const dateB = new Date(b.createdAt || 0).getTime();
    return dateB - dateA;
  });

  // Helper function to get media item for a job
  const getMediaItemForJob = (job: JobData) => {
    return mediaItems.find(item => item.id === job.mediaItemId);
  };

  // Handle notification prompt responses
  const handleEnableNotifications = async () => {
    markAsPromptedForNotifications();
    setShowNotificationPrompt(false);

    const permission = await requestNotificationPermission();
    if (permission === 'granted' && onNotificationsChange) {
      onNotificationsChange(true);
    }
  };

  const handleDeclineNotifications = () => {
    markAsPromptedForNotifications();
    setShowNotificationPrompt(false);
    if (onNotificationsChange) {
      onNotificationsChange(false);
    }
  };

  // Tray dimensions - calculate height based on number of jobs
  const calculateMobileHeight = (): number => {
    if (jobs.length === 0) return 300;

    // Base height for header, controls, and padding
    const baseHeight = 180; // Header + "All Jobs" section + padding

    // Height per job item (approximate)
    const jobItemHeight = 60;

    // Calculate total needed height
    const calculatedHeight = baseHeight + (jobs.length * jobItemHeight);

    // Cap at reasonable limits
    const maxHeight = window.innerHeight * 0.7; // 70% of screen
    const minHeight = 250; // Minimum height

    return Math.min(Math.max(calculatedHeight, minHeight), maxHeight);
  };

  const expandedHeight = {
    xs: `${calculateMobileHeight()}px`,
    sm: `${Math.min(calculateMobileHeight(), window.innerHeight * 0.6)}px`,
    md: `${window.innerHeight * 0.6}px`
  };


  // Always render the tray, even with no jobs

  return (
    <>
      {/* Mobile Backdrop */}
      <Backdrop
        open={!isCollapsed}
        onClick={onToggleCollapse}
        sx={{
          display: { xs: 'block', md: 'none' },
          zIndex: 1200
        }}
      />

      {/* Jobs Tray */}
      <Paper
        elevation={0}
        sx={{
          position: 'fixed',
          bottom: { xs: 12, md: 20 },
          left: {
            xs: 12, // Mobile: positioned from left
            md: 'auto' // Desktop: not positioned from left
          },
          right: {
            xs: isCollapsed ? 'auto' : 12, // Mobile: when expanded, extend to right edge
            md: 20 // Desktop: positioned from right
          },
          width: {
            xs: isCollapsed ? 'calc(50% - 18px)' : 'calc(100% - 24px)', // Half width when collapsed, full width when expanded
            md: '380px'
          },
          zIndex: 1300,
          backgroundColor: 'grey.50',
          border: 1,
          borderColor: 'divider',
          borderRadius: 1,
          transform: isCollapsed
            ? { xs: 'translateY(calc(100% - 48px))', md: 'translateY(calc(100% - 56px))' }
            : 'translateY(0)',
          opacity: 1,
          transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease-in-out, height 0.3s cubic-bezier(0.4, 0, 0.2, 1), max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1), width 0.3s cubic-bezier(0.4, 0, 0.2, 1), left 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          animation: 'fadeIn 0.3s ease-in-out',
          height: isCollapsed ? { xs: 48, md: 56 } : expandedHeight,
          maxHeight: isCollapsed ? { xs: 48, md: 56 } : expandedHeight,
          overflow: 'hidden',
          boxShadow: '0 -4px 20px rgba(0,0,0,0.15)',

          '@keyframes fadeIn': {
            '0%': {
              opacity: 0,
              transform: 'scale(0.95)'
            },
            '100%': {
              opacity: 1,
              transform: 'scale(1)'
            }
          }
        }}
      >
        {/* Minimized Tab */}
        {isCollapsed && (
          <Box
            onClick={onToggleCollapse}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              p: { xs: 1.5, md: 2 },
              cursor: 'pointer',
              minHeight: { xs: 48, md: 56 },
              boxSizing: 'border-box',
              '&:hover': {
                backgroundColor: 'action.hover'
              }
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 0.5, md: 1 } }}>
              <PlaylistPlay sx={{ fontSize: { xs: 18, md: 20 }, color: 'text.secondary' }} />
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 500,
                  fontSize: { xs: '0.9rem', md: '1.1rem' },
                  lineHeight: 1,
                  color: 'text.primary'
                }}
              >
                Jobs
              </Typography>
              {hasActiveJobs && (
                <Chip
                  label={activeJobs.length.toString()}
                  size="small"
                  color="primary"
                  sx={{
                    height: { xs: 18, md: 20 },
                    fontSize: { xs: '0.6rem', md: '0.7rem' },
                    minWidth: { xs: 18, md: 'auto' },
                    '& .MuiChip-label': {
                      px: { xs: 0.5, md: 1 }
                    }
                  }}
                />
              )}
            </Box>

            <KeyboardArrowUp sx={{ fontSize: 20, color: 'text.secondary', alignSelf: 'center' }} />
          </Box>
        )}

        {/* Expanded Content */}
        {!isCollapsed && (
          <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            {/* Header */}
            <Box
              onClick={onToggleCollapse}
              sx={{
                p: { xs: 1.5, md: 2 },
                borderBottom: 1,
                borderColor: 'divider',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                minHeight: { xs: 48, md: 56 },
                flexShrink: 0,
                cursor: 'pointer',
                backgroundColor: 'grey.50',
                '&:hover': {
                  backgroundColor: 'action.hover'
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 0.5, md: 1 } }}>
                <PlaylistPlay sx={{ fontSize: { xs: 18, md: 20 }, color: 'text.secondary' }} />
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 500,
                    fontSize: { xs: '0.9rem', md: '1.1rem' },
                    lineHeight: 1,
                    color: 'text.primary'
                  }}
                >
                  Jobs
                </Typography>
                {hasActiveJobs && (
                  <Chip
                    label={activeJobs.length.toString()}
                    size="small"
                    color="primary"
                    sx={{
                      height: { xs: 18, md: 20 },
                      fontSize: { xs: '0.6rem', md: '0.7rem' },
                      minWidth: { xs: 18, md: 'auto' },
                      '& .MuiChip-label': {
                        px: { xs: 0.5, md: 1 }
                      }
                    }}
                  />
                )}
              </Box>

              <IconButton
                onClick={(e) => {
                  e.stopPropagation(); // Prevent double-click when clicking the icon
                  onToggleCollapse();
                }}
                size="small"
                sx={{
                  color: 'text.secondary',
                  minWidth: { xs: 24, md: 32 },
                  minHeight: { xs: 24, md: 32 },
                  '&:hover': {
                    backgroundColor: 'action.hover'
                  }
                }}
              >
                <KeyboardArrowDown sx={{ fontSize: { xs: 16, md: 20 } }} />
              </IconButton>
            </Box>

            {/* Content Area */}
            <Box sx={{ 
              flex: 1, 
              overflow: 'auto',
              '&::-webkit-scrollbar': {
                width: 6
              },
              '&::-webkit-scrollbar-track': {
                backgroundColor: 'transparent'
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: 'action.disabled',
                borderRadius: 1
              }
            }}>
              {jobs.length === 0 ? (
                <Box sx={{ p: 4, textAlign: 'center' }}>
                  <PlaylistPlay sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    No compression jobs yet
                  </Typography>
                  <Typography variant="caption" color="text.disabled">
                    Start compressing media to see jobs here
                  </Typography>
                </Box>
              ) : (
                <>
                  {/* Header with Clear All button */}
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    p: 2,
                    pb: 1,
                    top: 0,
                    zIndex: 1,
                  }}>
                    <Typography variant="subtitle2" sx={{ 
                      fontSize: '0.8rem', 
                      fontWeight: 600, 
                      color: 'text.secondary' 
                    }}>
                      All Jobs
                    </Typography>
                    <Button
                      onClick={onClearAllJobs}
                      size="small"
                      variant="outlined"
                      sx={{
                        fontSize: '0.65rem',
                        minWidth: 'auto',
                        px: 1,
                        py: 0.25,
                        height: 'auto'
                      }}
                    >
                      Clear All
                    </Button>
                  </Box>

                  {/* Deletion Policy Notice */}
                  <Box sx={{ px: 2, pb: 1 }}>
                    <Typography
                      variant="caption"
                      sx={{
                        fontSize: '0.5rem',
                        color: 'text.disabled',
                        fontStyle: 'italic',
                        display: 'block',
                        lineHeight: 1.3
                      }}
                    >
                      Jobs are deleted after 24 hours
                    </Typography>
                  </Box>

                  {/* Individual Job Items */}
                  <List sx={{ p: 0 }}>
                    {sortedJobs.map((job, index) => (
                      <React.Fragment key={job.jobId}>
                        <JobItem
                          job={job}
                          mediaItem={getMediaItemForJob(job)}
                          token={token}
                          compact={false}
                          onClearJob={onClearJob}
                        />
                        {index < sortedJobs.length - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                </>
              )}
            </Box>
          </Box>
        )}
      </Paper>

      {/* Notification Permission Prompt */}
      <Dialog
        open={showNotificationPrompt}
        onClose={handleDeclineNotifications}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Notifications color="primary" />
            Enable Browser Notifications?
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Get browser notifications when compression jobs complete so you don't have to keep checking back.
          </Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            You can change this setting later in your account preferences.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeclineNotifications} color="inherit">
            No Thanks
          </Button>
          <Button onClick={handleEnableNotifications} variant="contained">
            Enable Browser Notifications
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default JobsTray;
