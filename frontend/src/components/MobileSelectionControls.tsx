import React from 'react';
import {
  Button,
  Paper,
  Fade
} from '@mui/material';
import {
  SelectAll,
  Clear
} from '@mui/icons-material';

interface MobileSelectionControlsProps {
  // Selection state
  selectedItems: Set<string>;
  totalItems: number;

  // Handlers
  onSelectAll: () => void;
  onClearSelection: () => void;

  // Visibility
  show: boolean;
}

const MobileSelectionControls: React.FC<MobileSelectionControlsProps> = ({
  selectedItems,
  totalItems,
  onSelectAll,
  onClearSelection,
  show
}) => {
  const selectedCount = selectedItems.size;
  const hasSelection = selectedCount > 0;
  const allSelected = selectedCount === totalItems && totalItems > 0;

  if (!show || !hasSelection) {
    return null;
  }

  return (
    <Fade in={show && hasSelection}>
      <Paper
        elevation={4}
        sx={{
          position: 'fixed',
          bottom: 80, // Above the trays (56px tray height + 12px margin + 12px spacing)
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 1250, // Above trays but below backdrop
          backgroundColor: 'background.paper',
          borderRadius: 2,
          p: 1,
          display: { xs: 'flex', md: 'none' }, // Only show on mobile
          gap: 1,
          boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
          border: '1px solid',
          borderColor: 'divider'
        }}
      >
        <Button
          startIcon={<SelectAll />}
          onClick={onSelectAll}
          disabled={allSelected || totalItems === 0}
          size="small"
          variant={allSelected ? "contained" : "outlined"}
          sx={{
            minWidth: 'auto',
            fontSize: '0.75rem',
            px: 1.5,
            py: 0.5,
            textTransform: 'none'
          }}
        >
          {allSelected ? 'All Selected' : 'All'}
        </Button>
        
        <Button
          startIcon={<Clear />}
          onClick={onClearSelection}
          size="small"
          variant="outlined"
          color="error"
          sx={{
            minWidth: 'auto',
            fontSize: '0.75rem',
            px: 1.5,
            py: 0.5,
            textTransform: 'none'
          }}
        >
          Clear
        </Button>
      </Paper>
    </Fade>
  );
};

export default MobileSelectionControls;
