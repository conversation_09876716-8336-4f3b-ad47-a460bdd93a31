import React from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Chip,
  Button,
  Backdrop,
  Badge
} from '@mui/material';
import {
  Settings,
  KeyboardArrowDown,
  KeyboardArrowUp,
  Compress,
  ExitToApp,
  CleaningServices
} from '@mui/icons-material';

interface OperationsTrayProps {
  // Panel state
  isCollapsed: boolean;
  onToggleCollapse: () => void;

  // Selection state
  selectedItems: Set<string>;
  totalItems: number;
  expiredItemsCount: number;

  // Handlers for operations
  onSelectAll: () => void;
  onClearSelection: () => void;
  onCompressSelected: () => void;
  onRemoveSelected: () => void;
  onClearExpired: () => void;
}

const OperationsTray: React.FC<OperationsTrayProps> = ({
  isCollapsed,
  onToggleCollapse,
  selectedItems,
  totalItems,
  expiredItemsCount,
  onSelectAll,
  onClearSelection,
  onCompressSelected,
  onRemoveSelected,
  onClearExpired
}) => {
  const selectedCount = selectedItems.size;
  const hasSelection = selectedCount > 0;
  const hasExpiredItems = expiredItemsCount > 0;

  // Tray dimensions
  const expandedHeight = {
    xs: '300px',
    sm: '280px',
    md: '260px'
  };
  // Responsive minimized height for mobile vs desktop

  return (
    <>
      {/* Mobile Backdrop */}
      <Backdrop
        open={!isCollapsed}
        onClick={onToggleCollapse}
        sx={{
          display: { xs: 'block', md: 'none' },
          zIndex: 1200
        }}
      />

      {/* Operations Tray */}
      <Paper
        elevation={0}
        sx={{
          position: 'fixed',
          bottom: { xs: 12, md: 20 },
          right: { xs: 12, md: 20 },
          width: { xs: 'calc(50% - 18px)', md: '300px' }, // Always half width on mobile
          zIndex: 1300,
          backgroundColor: 'grey.50',
          border: 1,
          borderColor: 'divider',
          borderRadius: 1,
          transform: isCollapsed
            ? { xs: 'translateY(calc(100% - 48px))', md: 'translateY(calc(100% - 56px))' }
            : 'translateY(0)',
          opacity: 1,
          transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease-in-out, height 0.3s cubic-bezier(0.4, 0, 0.2, 1), max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          animation: 'fadeIn 0.3s ease-in-out',
          height: isCollapsed ? { xs: 48, md: 56 } : expandedHeight,
          maxHeight: isCollapsed ? { xs: 48, md: 56 } : expandedHeight,
          overflow: 'hidden',
          boxShadow: '0 -4px 20px rgba(0,0,0,0.15)',

          '@keyframes fadeIn': {
            '0%': {
              opacity: 0,
              transform: 'scale(0.95)'
            },
            '100%': {
              opacity: 1,
              transform: 'scale(1)'
            }
          }
        }}
      >
        {/* Collapsed Header - Only visible when collapsed */}
        {isCollapsed && (
          <Box
            onClick={onToggleCollapse}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              p: { xs: 1.5, md: 2 },
              cursor: 'pointer',
              minHeight: { xs: 48, md: 56 },
              boxSizing: 'border-box',
              '&:hover': {
                backgroundColor: 'action.hover'
              }
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 0.5, md: 1 } }}>
              <Settings sx={{ fontSize: { xs: 18, md: 20 }, color: 'text.secondary' }} />
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 500,
                  fontSize: { xs: '0.9rem', md: '1.1rem' },
                  lineHeight: 1,
                  color: 'text.primary'
                }}
              >
                Operations
              </Typography>
              {hasSelection && (
                <Chip
                  label={selectedCount.toString()}
                  size="small"
                  color="primary"
                  sx={{
                    height: { xs: 18, md: 20 },
                    fontSize: { xs: '0.6rem', md: '0.7rem' },
                    minWidth: { xs: 18, md: 'auto' },
                    '& .MuiChip-label': {
                      px: { xs: 0.5, md: 1 }
                    }
                  }}
                />
              )}
            </Box>

            <KeyboardArrowUp sx={{ fontSize: 20, color: 'text.secondary', alignSelf: 'center' }} />
          </Box>
        )}

        {/* Expanded Content */}
        {!isCollapsed && (
          <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            {/* Header */}
            <Box
              onClick={onToggleCollapse}
              sx={{
                p: { xs: 1.5, md: 2 },
                borderBottom: 1,
                borderColor: 'divider',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                minHeight: { xs: 48, md: 56 },
                flexShrink: 0,
                cursor: 'pointer',
                backgroundColor: 'grey.50',
                '&:hover': {
                  backgroundColor: 'action.hover'
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 0.5, md: 1 } }}>
                <Settings sx={{ fontSize: { xs: 18, md: 20 }, color: 'text.secondary' }} />
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 500,
                    fontSize: { xs: '0.9rem', md: '1.1rem' },
                    lineHeight: 1,
                    color: 'text.primary'
                  }}
                >
                  Operations
                </Typography>
                {hasSelection && (
                  <Chip
                    label={selectedCount.toString()}
                    size="small"
                    color="primary"
                    sx={{
                      height: { xs: 18, md: 20 },
                      fontSize: { xs: '0.6rem', md: '0.7rem' },
                      minWidth: { xs: 18, md: 'auto' },
                      '& .MuiChip-label': {
                        px: { xs: 0.5, md: 1 }
                      }
                    }}
                  />
                )}
              </Box>

              <IconButton
                onClick={(e) => {
                  e.stopPropagation(); // Prevent double-click when clicking the icon
                  onToggleCollapse();
                }}
                size="small"
                sx={{
                  color: 'text.secondary',
                  minWidth: { xs: 24, md: 32 },
                  minHeight: { xs: 24, md: 32 },
                  alignSelf: 'center',
                  '&:hover': {
                    backgroundColor: 'action.hover'
                  }
                }}
              >
                <KeyboardArrowDown sx={{ fontSize: { xs: 16, md: 20 } }} />
              </IconButton>
            </Box>

            {/* Content Area */}
            <Box sx={{
              flex: 1,
              overflow: 'auto',
              p: 2,
              pt: 1,
              '&::-webkit-scrollbar': {
                width: 6
              },
              '&::-webkit-scrollbar-track': {
                backgroundColor: 'transparent'
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: 'action.disabled',
                borderRadius: 1
              }
            }}>
            {/* Action Operations */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1, color: 'text.secondary' }}>
                Actions
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexDirection: 'column' }}>
                <Button
                  startIcon={<Compress />}
                  onClick={onCompressSelected}
                  disabled={!hasSelection}
                  size="small"
                  variant="contained"
                  color="primary"
                  fullWidth
                  sx={{
                    minHeight: 40,
                    fontSize: '0.8rem'
                  }}
                >
                  Compress
                </Button>
                <Button
                  startIcon={<ExitToApp />}
                  onClick={onRemoveSelected}
                  disabled={!hasSelection}
                  size="small"
                  variant="outlined"
                  color="error"
                  fullWidth
                  sx={{
                    minHeight: 40,
                    fontSize: '0.8rem'
                  }}
                >
                  Remove
                </Button>
              </Box>
            </Box>

            {/* Cleanup Operations */}
            {hasExpiredItems && (
              <Box>
                <Typography variant="subtitle2" sx={{ mb: 1, color: 'text.secondary' }}>
                  Cleanup
                </Typography>
                <Button
                  startIcon={
                    <Badge badgeContent={expiredItemsCount} color="warning" max={99}>
                      <CleaningServices />
                    </Badge>
                  }
                  onClick={onClearExpired}
                  size="small"
                  variant="outlined"
                  color="warning"
                  fullWidth
                  sx={{
                    minHeight: 40,
                    fontSize: '0.8rem'
                  }}
                >
                  Clear Expired Items
                </Button>
              </Box>
            )}
            </Box>
          </Box>
        )}
      </Paper>
    </>
  );
};

export default OperationsTray;
