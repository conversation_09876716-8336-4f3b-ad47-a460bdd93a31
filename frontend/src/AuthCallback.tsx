import React, { useEffect, useState, useRef } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Container, Typography, CircularProgress, Box } from '@mui/material';

const AuthCallback: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing authentication...');
  const hasProcessed = useRef(false);

  useEffect(() => {
    const handleCallback = async () => {
      // Prevent duplicate processing in StrictMode
      if (hasProcessed.current) {
        console.log('AuthCallback: Already processed, skipping duplicate call');
        return;
      }
      hasProcessed.current = true;
      try {
        const code = searchParams.get('code');
        const error = searchParams.get('error');

        if (error) {
          setStatus('error');
          setMessage(`Authentication failed: ${error}`);
          return;
        }

        if (!code) {
          setStatus('error');
          setMessage('No authorization code received');
          return;
        }

        console.log('Received authorization code:', code.substring(0, 20) + '...');

        // Send the authorization code to the backend
        const response = await fetch('/api/auth/oauth-callback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ code })
        });

        console.log('OAuth callback response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('OAuth callback error:', errorText);
          setStatus('error');
          setMessage(`Authentication failed: ${response.status} - ${errorText}`);
          return;
        }

        const data = await response.json();
        console.log('OAuth callback success:', data);

        // Handle Firebase custom token authentication
        if ((window as any).handleAuthCallback) {
          await (window as any).handleAuthCallback(data.token);
        }

        setStatus('success');
        setMessage('Authentication successful! Redirecting...');

        // Redirect to home page after a short delay
        setTimeout(() => {
          navigate('/');
        }, 2000);

      } catch (err) {
        console.error('OAuth callback error:', err);
        setStatus('error');
        setMessage(`Authentication failed: ${err instanceof Error ? err.message : 'Unknown error'}`);
      }
    };

    handleCallback();
  }, [searchParams, navigate]);

  return (
    <Container maxWidth="sm" sx={{ mt: 8, textAlign: 'center' }}>
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 3 }}>
        
        {status === 'loading' && (
          <>
            <CircularProgress size={60} />
            <Typography variant="h6" color="text.secondary">
              {message}
            </Typography>
          </>
        )}
        
        {status === 'success' && (
          <>
            <Typography variant="h6" color="success.main">
              ✓ {message}
            </Typography>
          </>
        )}
        
        {status === 'error' && (
          <>
            <Typography variant="h6" color="error.main">
              ✗ {message}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              You can close this window and try again.
            </Typography>
          </>
        )}
      </Box>
    </Container>
  );
};

export default AuthCallback;
