import { signInWithCustomToken, signOut, onAuthStateChanged, User } from 'firebase/auth';
import { auth } from '../firebase';

class AuthService {
  private auth = auth;
  private currentUser: User | null = null;
  private authStateListeners: ((user: User | null) => void)[] = [];

  constructor() {
    // Listen for auth state changes
    onAuthStateChanged(this.auth, (user) => {
      this.currentUser = user;
      this.authStateListeners.forEach(listener => listener(user));
    });
  }

  /**
   * Sign in with a Firebase custom token received from the backend
   */
  async signInWithCustomToken(customToken: string): Promise<User> {
    try {
      console.log('Signing in with Firebase custom token...');
      const userCredential = await signInWithCustomToken(this.auth, customToken);
      console.log('Successfully signed in with Firebase:', userCredential.user.uid);
      return userCredential.user;
    } catch (error) {
      console.error('Error signing in with custom token:', error);
      throw error;
    }
  }

  /**
   * Sign out the current user
   */
  async signOut(): Promise<void> {
    try {
      await signOut(this.auth);
      console.log('Successfully signed out from Firebase');
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  }

  /**
   * Get the current Firebase ID token for API requests
   */
  async getIdToken(): Promise<string | null> {
    if (!this.currentUser) {
      return null;
    }

    try {
      const idToken = await this.currentUser.getIdToken();
      return idToken;
    } catch (error) {
      console.error('Error getting ID token:', error);
      return null;
    }
  }

  /**
   * Get the current user
   */
  getCurrentUser(): User | null {
    return this.currentUser;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.currentUser !== null;
  }

  /**
   * Listen for authentication state changes
   */
  onAuthStateChange(callback: (user: User | null) => void): () => void {
    this.authStateListeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.authStateListeners.indexOf(callback);
      if (index > -1) {
        this.authStateListeners.splice(index, 1);
      }
    };
  }

  /**
   * Get user information from Firebase Auth
   */
  getUserInfo() {
    if (!this.currentUser) {
      return null;
    }

    return {
      uid: this.currentUser.uid,
      email: this.currentUser.email,
      displayName: this.currentUser.displayName,
      photoURL: this.currentUser.photoURL,
      emailVerified: this.currentUser.emailVerified
    };
  }
}

// Export a singleton instance
export const authService = new AuthService();
export default authService;
