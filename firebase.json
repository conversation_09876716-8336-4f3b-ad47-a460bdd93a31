{"hosting": {"public": "frontend/build", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "headers": [{"source": "**", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Content-Security-Policy", "value": "frame-ancestors 'self' https://accounts.google.com"}]}], "rewrites": [{"source": "/api/**", "run": {"serviceId": "vidcompressor-api-server", "region": "us-central1"}}, {"source": "**", "destination": "/index.html"}]}, "emulators": {"firestore": {"port": 8080}, "hosting": {"port": 5000}, "ui": {"enabled": true}, "singleProjectMode": true, "pubsub": {"port": 8085}}}