rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Helper function to check if user owns the resource
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // Users collection - users can read/write their own user document
    match /users/{userId} {
      allow read, write: if isOwner(userId);
    }
    
    // Compression jobs - users can read/write their own jobs
    match /compressionJobs/{jobId} {
      allow read, write: if isAuthenticated() && 
        (resource == null || resource.data.userId == request.auth.uid);
      
      // Allow creation if the userId in the document matches the authenticated user
      allow create: if isAuthenticated() && 
        request.resource.data.userId == request.auth.uid;
    }
    
    // User media items - users can read/write their own media items
    match /userMediaItems/{mediaItemId} {
      allow read, write: if isAuthenticated() && 
        (resource == null || resource.data.userId == request.auth.uid);
      
      // Allow creation if the userId in the document matches the authenticated user
      allow create: if isAuthenticated() && 
        request.resource.data.userId == request.auth.uid;
    }
    
    // Email signups - allow creation for anyone (for newsletter signup)
    // Only allow reading for authenticated users (admin functionality)
    match /emailSignups/{signupId} {
      allow create: if true; // Anyone can sign up for newsletter
      allow read: if isAuthenticated(); // Only authenticated users can read
      allow update, delete: if false; // No updates or deletes allowed
    }
    
    // Credit transactions - users can read their own transactions
    // Only backend can write (through admin SDK)
    match /creditTransactions/{transactionId} {
      allow read: if isAuthenticated() && 
        (resource == null || resource.data.userId == request.auth.uid);
      allow write: if false; // Only backend can write transactions
    }
    
    // Credit costs - read-only for all authenticated users
    // This contains pricing information that all users need to see
    match /creditCosts/{costId} {
      allow read: if isAuthenticated();
      allow write: if false; // Only backend can update pricing
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
